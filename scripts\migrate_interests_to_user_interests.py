#!/usr/bin/env python
"""
Script para migrar datos del campo deprecado 'interests' (JSONField) 
al nuevo campo 'user_interests' (ManyToManyField) en EventScheduleEnrollment.

Este script debe ejecutarse desde Django shell:
python manage.py shell < scripts/migrate_interests_to_user_interests.py

O importando el script en Django shell:
python manage.py shell
>>> exec(open('scripts/migrate_interests_to_user_interests.py').read())
"""

import re
from django.db import transaction
from core.models import EventScheduleEnrollment, Interest

# Opciones de especialización válidas
SPECIALIZATION_OPTIONS = [
    "PE Bolsa de Valores",
    "PE Comercio Internacional y Aduanas",
    "PE Data Sciencie - Data Mining - Data Analytics",
    "PE Econometría",
    "PE Economía Avanzada",
    "PE Finanzas Avanzadas",
    "PE Gestión Pública",
    "PE Manejo de Software (Excel, VBA, R, Python, etc)",
    "PE Proyectos de Inversión",
    "PF Formación Universitaria",
    "PP Cursos de Extensión Universitaria",
]


def create_interest_records():
    """
    Crear registros Interest para todas las opciones de especialización si no existen.
    """
    print("Creando registros Interest...")
    created_count = 0
    
    for option in SPECIALIZATION_OPTIONS:
        interest, created = Interest.objects.get_or_create(
            name=option,
            defaults={'code_name': None}
        )
        if created:
            created_count += 1
            print(f"  ✓ Creado: {option}")
        else:
            print(f"  - Ya existe: {option}")
    
    print(f"Total registros Interest creados: {created_count}")
    return created_count


def clean_and_normalize_interests(interests_list):
    """
    Limpiar y normalizar la lista de intereses.
    
    Args:
        interests_list (list): Lista de intereses del JSONField
        
    Returns:
        list: Lista de intereses limpiados y normalizados
    """
    if not interests_list:
        return []
    
    cleaned_interests = []
    
    for interest in interests_list:
        if not interest or not isinstance(interest, str):
            continue
            
        # Limpiar espacios
        interest = interest.strip()
        
        # Caso especial: unir el splitting incorrecto del manejo de software
        if interest.startswith("Pe Manejo De Software (excel"):
            # Buscar los siguientes elementos que forman parte de este interés
            continue  # Lo manejaremos de forma especial
        elif interest in ["Python", "R", "Etc)"]:
            # Estos son parte del interés anterior, los saltamos
            continue
        
        # Normalizar prefijos a mayúsculas
        if interest.lower().startswith("pe "):
            interest = "PE " + interest[3:]
        elif interest.lower().startswith("pp "):
            interest = "PP " + interest[3:]
        elif interest.lower().startswith("pf "):
            interest = "PF " + interest[3:]
        
        cleaned_interests.append(interest)
    
    # Manejar el caso especial del manejo de software
    has_software_parts = any(
        "Pe Manejo De Software (excel" in str(item) or 
        item in ["Python", "R", "Etc)"] 
        for item in interests_list
    )
    
    if has_software_parts:
        cleaned_interests.append("PE Manejo de Software (Excel, VBA, R, Python, etc)")
    
    return cleaned_interests


def map_interest_to_valid_option(interest):
    """
    Mapear un interés a una opción válida de especialización.
    
    Args:
        interest (str): Interés a mapear
        
    Returns:
        str or None: Opción válida encontrada o None si no hay match
    """
    if not interest:
        return None
    
    # Normalizar para comparación
    interest_lower = interest.lower().strip()
    
    # Buscar match exacto (case insensitive)
    for option in SPECIALIZATION_OPTIONS:
        if option.lower() == interest_lower:
            return option
    
    # Buscar match parcial por palabras clave
    keyword_mapping = {
        "bolsa": "PE Bolsa de Valores",
        "comercio": "PE Comercio Internacional y Aduanas",
        "data": "PE Data Sciencie - Data Mining - Data Analytics",
        "econometría": "PE Econometría",
        "econometria": "PE Econometría",
        "economía avanzada": "PE Economía Avanzada",
        "economia avanzada": "PE Economía Avanzada",
        "finanzas": "PE Finanzas Avanzadas",
        "gestión pública": "PE Gestión Pública",
        "gestion publica": "PE Gestión Pública",
        "software": "PE Manejo de Software (Excel, VBA, R, Python, etc)",
        "excel": "PE Manejo de Software (Excel, VBA, R, Python, etc)",
        "python": "PE Manejo de Software (Excel, VBA, R, Python, etc)",
        "proyectos": "PE Proyectos de Inversión",
        "inversión": "PE Proyectos de Inversión",
        "inversion": "PE Proyectos de Inversión",
        "formación": "PF Formación Universitaria",
        "formacion": "PF Formación Universitaria",
        "universitaria": "PF Formación Universitaria",
        "extensión": "PP Cursos de Extensión Universitaria",
        "extension": "PP Cursos de Extensión Universitaria",
        "cursos": "PP Cursos de Extensión Universitaria",
    }
    
    for keyword, option in keyword_mapping.items():
        if keyword in interest_lower:
            return option
    
    return None


def migrate_enrollment_interests():
    """
    Migrar intereses de todos los enrollments del campo interests al campo user_interests.
    """
    print("\nIniciando migración de intereses...")
    
    # Obtener todos los enrollments que tienen datos en interests
    enrollments = EventScheduleEnrollment.objects.filter(
        interests__isnull=False
    ).exclude(interests=[])
    
    total_enrollments = enrollments.count()
    print(f"Total enrollments a procesar: {total_enrollments}")
    
    migrated_count = 0
    error_count = 0
    
    for i, enrollment in enumerate(enrollments, 1):
        try:
            print(f"\nProcesando enrollment {i}/{total_enrollments} (ID: {enrollment.pk})")
            print(f"  Intereses originales: {enrollment.interests}")
            
            # Limpiar y normalizar intereses
            cleaned_interests = clean_and_normalize_interests(enrollment.interests)
            print(f"  Intereses limpiados: {cleaned_interests}")
            
            # Mapear a opciones válidas y obtener objetos Interest
            interest_objects = []
            for interest in cleaned_interests:
                mapped_option = map_interest_to_valid_option(interest)
                if mapped_option:
                    try:
                        interest_obj = Interest.objects.get(name=mapped_option, deleted=False)
                        interest_objects.append(interest_obj)
                        print(f"    ✓ Mapeado: {interest} -> {mapped_option}")
                    except Interest.DoesNotExist:
                        print(f"    ✗ No encontrado objeto Interest para: {mapped_option}")
                else:
                    print(f"    ✗ No se pudo mapear: {interest}")
            
            # Establecer la relación many-to-many
            if interest_objects:
                enrollment.user_interests.set(interest_objects)
                print(f"  ✓ Establecidos {len(interest_objects)} intereses en user_interests")
                migrated_count += 1
            else:
                print(f"  - No se encontraron intereses válidos para migrar")
                
        except Exception as e:
            print(f"  ✗ Error procesando enrollment {enrollment.pk}: {e}")
            error_count += 1
    
    print(f"\n=== RESUMEN DE MIGRACIÓN ===")
    print(f"Total enrollments procesados: {total_enrollments}")
    print(f"Enrollments migrados exitosamente: {migrated_count}")
    print(f"Enrollments con errores: {error_count}")
    print(f"Enrollments sin intereses válidos: {total_enrollments - migrated_count - error_count}")


def main():
    """
    Función principal que ejecuta todo el proceso de migración.
    """
    print("=== MIGRACIÓN DE INTERESES A USER_INTERESTS ===")
    print("Este script migrará datos del campo 'interests' al campo 'user_interests'")
    print("Manteniendo el campo 'interests' intacto (migración soft)")
    
    try:
        with transaction.atomic():
            # Paso 1: Crear registros Interest
            create_interest_records()
            
            # Paso 2: Migrar intereses de enrollments
            migrate_enrollment_interests()
            
        print("\n✅ Migración completada exitosamente!")
        
    except Exception as e:
        print(f"\n❌ Error durante la migración: {e}")
        raise


if __name__ == "__main__":
    main()
