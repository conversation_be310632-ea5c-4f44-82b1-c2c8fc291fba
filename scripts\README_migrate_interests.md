# Migración de Intereses: interests → user_interests

Este documento describe el proceso de migración del campo deprecado `interests` (JSONField) al nuevo campo `user_interests` (ManyToManyField) en el modelo `EventScheduleEnrollment`.

## Contexto

El campo `interests` en `EventScheduleEnrollment` era un JSONField que almacenaba una lista de strings con los intereses del usuario. Este campo presentaba varios problemas:

- Datos inconsistentes debido a errores de splitting
- Problemas de mayúsculas/minúsculas (Pe vs PE, Pp vs PP, Pf vs PF)
- Dificultad para realizar cálculos y consultas eficientes
- Falta de normalización de datos

Para solucionar estos problemas, se implementó:

1. Un nuevo modelo `Interest` con campos `name` y `code_name`
2. Una relación ManyToMany `user_interests` en `EventScheduleEnrollment`
3. Actualización de serializers para manejar ambos campos durante la transición

## Problemas Identificados en los Datos

### 1. Splitting Incorrecto
Algunos registros tienen datos como:
```json
["Pe Manejo De Software (excel", "Python", "R", "Etc)", "PE Proyectos de Inversión"]
```

**Solución**: El script detecta este patrón y lo une correctamente como:
`"PE Manejo de Software (Excel, VBA, R, Python, etc)"`

### 2. Inconsistencias de Mayúsculas
- `Pe` → `PE`
- `Pp` → `PP` 
- `Pf` → `PF`

**Solución**: El script normaliza todos los prefijos a mayúsculas.

## Intereses Válidos

Los intereses oficiales que deben existir en la tabla `Interest` son:

```python
SPECIALIZATION_OPTIONS = [
    "PE Bolsa de Valores",
    "PE Comercio Internacional y Aduanas",
    "PE Data Sciencie - Data Mining - Data Analytics",
    "PE Econometría",
    "PE Economía Avanzada",
    "PE Finanzas Avanzadas",
    "PE Gestión Pública",
    "PE Manejo de Software (Excel, VBA, R, Python, etc)",
    "PE Proyectos de Inversión",
    "PF Formación Universitaria",
    "PP Cursos de Extensión Universitaria",
]
```

## Ejecución del Script

### Opción 1: Desde Django Shell
```bash
python manage.py shell < scripts/migrate_interests_to_user_interests.py
```

### Opción 2: Importar en Django Shell
```bash
python manage.py shell
```
```python
>>> exec(open('scripts/migrate_interests_to_user_interests.py').read())
```

### Opción 3: Ejecutar funciones específicas
```bash
python manage.py shell
```
```python
>>> from scripts.migrate_interests_to_user_interests import *
>>> create_interest_records()  # Solo crear registros Interest
>>> migrate_enrollment_interests()  # Solo migrar enrollments
```

## Proceso de Migración

El script ejecuta los siguientes pasos:

### 1. Creación de Registros Interest
- Crea registros `Interest` para cada opción de especialización válida
- Usa `get_or_create` para evitar duplicados
- Establece `code_name` como `None` inicialmente

### 2. Migración de Enrollments
Para cada `EventScheduleEnrollment` con datos en `interests`:

1. **Limpieza**: Remueve espacios, maneja casos especiales
2. **Normalización**: Corrige mayúsculas en prefijos
3. **Mapeo**: Encuentra la opción válida correspondiente
4. **Relación**: Establece la relación ManyToMany con objetos `Interest`

### 3. Mapeo de Intereses

El script usa dos estrategias de mapeo:

1. **Match Exacto**: Comparación case-insensitive directa
2. **Match por Palabras Clave**: Busca keywords específicas

Ejemplos de mapeo por keywords:
- `"bolsa"` → `"PE Bolsa de Valores"`
- `"software"` → `"PE Manejo de Software (Excel, VBA, R, Python, etc)"`
- `"formacion"` → `"PF Formación Universitaria"`

## Características del Script

### Seguridad
- ✅ **Transaccional**: Usa `transaction.atomic()` para rollback en caso de error
- ✅ **Idempotente**: Se puede ejecutar múltiples veces sin problemas
- ✅ **No destructivo**: Mantiene el campo `interests` intacto
- ✅ **Logging detallado**: Muestra progreso y errores

### Validaciones
- Verifica que los objetos `Interest` existan antes de crear relaciones
- Maneja errores gracefully y continúa con el siguiente registro
- Proporciona estadísticas detalladas al final

## Salida del Script

El script proporciona información detallada durante la ejecución:

```
=== MIGRACIÓN DE INTERESES A USER_INTERESTS ===
Creando registros Interest...
  ✓ Creado: PE Bolsa de Valores
  - Ya existe: PE Comercio Internacional y Aduanas
...

Procesando enrollment 1/150 (ID: abc-123)
  Intereses originales: ["Pe Manejo De Software (excel", "Python", "R", "Etc)"]
  Intereses limpiados: ["PE Manejo de Software (Excel, VBA, R, Python, etc)"]
    ✓ Mapeado: PE Manejo de Software (Excel, VBA, R, Python, etc) -> PE Manejo de Software (Excel, VBA, R, Python, etc)
  ✓ Establecidos 1 intereses en user_interests

=== RESUMEN DE MIGRACIÓN ===
Total enrollments procesados: 150
Enrollments migrados exitosamente: 142
Enrollments con errores: 2
Enrollments sin intereses válidos: 6
```

## Post-Migración

Después de ejecutar el script:

1. **Verificar datos**: Revisar que las relaciones se crearon correctamente
2. **Probar serializers**: Confirmar que los nuevos serializers funcionan
3. **Monitorear**: Observar el comportamiento en producción
4. **Planificar deprecación**: Eventualmente remover el campo `interests`

## Rollback

Si es necesario hacer rollback:

```python
# Limpiar todas las relaciones user_interests
from core.models import EventScheduleEnrollment
for enrollment in EventScheduleEnrollment.objects.all():
    enrollment.user_interests.clear()

# Opcional: Eliminar registros Interest creados
from core.models import Interest
Interest.objects.filter(code_name__isnull=True).delete()
```

## Notas Importantes

- ⚠️ **NO ejecutar en producción sin backup**
- ⚠️ **Probar primero en ambiente de desarrollo**
- ⚠️ **El campo `interests` se mantiene para compatibilidad**
- ⚠️ **Los serializers manejan ambos campos durante la transición**
